# [1.12.0](https://github.com/14790897/handwriting-web/compare/v1.11.1...v1.12.0) (2025-07-12)


### Features

* 一键启动tasks.json ([bedde2b](https://github.com/14790897/handwriting-web/commit/bedde2b7e2a3fa6f19e92d06a7d604134ab415de))

## [1.11.1](https://github.com/14790897/handwriting-web/compare/v1.11.0...v1.11.1) (2025-07-08)


### Bug Fixes

* 响应式 ([caed098](https://github.com/14790897/handwriting-web/commit/caed09833a3ea65d102f01b70e04b34977116676))

# [1.11.0](https://github.com/14790897/handwriting-web/compare/v1.10.3...v1.11.0) (2025-07-08)


### Features

* cooldown ([04884d2](https://github.com/14790897/handwriting-web/commit/04884d2eba610d48ee6475c9c2d7fb2daf81fa24))

## [1.10.3](https://github.com/14790897/handwriting-web/compare/v1.10.2...v1.10.3) (2025-07-08)


### Bug Fixes

* rm zip ([ee944c8](https://github.com/14790897/handwriting-web/commit/ee944c8c207ec95a65fa198157df099c8ae9a5af))

## [1.10.2](https://github.com/14790897/handwriting-web/compare/v1.10.1...v1.10.2) (2025-07-08)


### Bug Fixes

* 截断 ([054003f](https://github.com/14790897/handwriting-web/commit/054003f93729dedb63f115ab5341b3e0e425ff0e))

## [1.10.1](https://github.com/14790897/handwriting-web/compare/v1.10.0...v1.10.1) (2025-07-08)


### Bug Fixes

* 后端清理文件失败 ([7b97483](https://github.com/14790897/handwriting-web/commit/7b97483a11aa42b45a2fa8abe2c3c150ec5f7569))

# [1.10.0](https://github.com/14790897/handwriting-web/compare/v1.9.0...v1.10.0) (2025-07-08)


### Features

* 主站最多一次十页 ([25bad47](https://github.com/14790897/handwriting-web/commit/25bad4771f53b4960c789eb40e85a5cce8a218cf))

# [1.9.0](https://github.com/14790897/handwriting-web/compare/v1.8.0...v1.9.0) (2025-07-08)


### Features

* pwa ([cfe12ad](https://github.com/14790897/handwriting-web/commit/cfe12ad0215ea1eb3c33564d30c7974edb4cbb66))

# [1.8.0](https://github.com/14790897/handwriting-web/compare/v1.7.2...v1.8.0) (2025-07-08)


### Features

* icon manifest ([d96989e](https://github.com/14790897/handwriting-web/commit/d96989e649e0a7524ae9d3d8fa3045c4486bd1c6))

## [1.7.2](https://github.com/14790897/handwriting-web/compare/v1.7.1...v1.7.2) (2025-07-07)


### Bug Fixes

* 容器目录挂载 ([7921a79](https://github.com/14790897/handwriting-web/commit/7921a793ff6fed75a1d924f956035a2e6d810bf7))

## [1.7.1](https://github.com/14790897/handwriting-web/compare/v1.7.0...v1.7.1) (2025-07-07)


### Bug Fixes

* 404 not found because nginx rule ([a95d3af](https://github.com/14790897/handwriting-web/commit/a95d3afb9a559c5d23be06abc28dddbd03e25042))

# [1.7.0](https://github.com/14790897/handwriting-web/compare/v1.6.0...v1.7.0) (2025-06-19)


### Features

* seo ([1018480](https://github.com/14790897/handwriting-web/commit/1018480968dcebbb1461b2f4121473549ea26564))

# [1.6.0](https://github.com/14790897/handwriting-web/compare/v1.5.3...v1.6.0) (2025-05-27)


### Features

* 增加arm平台构建 ([5012446](https://github.com/14790897/handwriting-web/commit/50124467831188cb5b116230de1347dabf17fcfe))

## [1.5.3](https://github.com/14790897/handwriting-web/compare/v1.5.2...v1.5.3) (2025-05-27)


### Bug Fixes

* pdf图片大小应该为原图格式 ([e28383d](https://github.com/14790897/handwriting-web/commit/e28383d411a254fccfdfd434ed1a1aa7c440e181))

## [1.5.2](https://github.com/14790897/handwriting-web/compare/v1.5.1...v1.5.2) (2025-02-19)


### Bug Fixes

* 防止邮件重复发送，以及提示免费 ([3dad59c](https://github.com/14790897/handwriting-web/commit/3dad59cfb5697610bb26c9f256d4c4eea5f62343))

## [1.5.1](https://github.com/14790897/handwriting-web/compare/v1.5.0...v1.5.1) (2025-02-15)


### Bug Fixes

* 重置时不清空text ([c7aac8b](https://github.com/14790897/handwriting-web/commit/c7aac8bbdd2e9097525aac480a94348c7f40c668))

# [1.5.0](https://github.com/14790897/handwriting-web/compare/v1.4.0...v1.5.0) (2025-02-14)


### Bug Fixes

*  反馈邮件发送地址 ([e2f906d](https://github.com/14790897/handwriting-web/commit/e2f906d6e60ed88b22b33cc4d468e858af0abbd7))
* clarity不能有报错才能运行 ([75760a1](https://github.com/14790897/handwriting-web/commit/75760a13e9a8a0d13bcd02cc23d5b8e683361ba1))
* clartity需要两个脚本开启 ([fb78b2e](https://github.com/14790897/handwriting-web/commit/fb78b2ec20d7489d50384e0e7e219cfd66609b1b))
* seo ([8c551f0](https://github.com/14790897/handwriting-web/commit/8c551f0fb959cc95aad92f5a6c23f117f66c4263))
* version update ([80f7ad0](https://github.com/14790897/handwriting-web/commit/80f7ad0f97cd3fb2cc59b845ae24eb06a7e01a41))
* 一个日志格式化错误 ([aee6b20](https://github.com/14790897/handwriting-web/commit/aee6b206402e3795399f0c1e3b3bb9f03bfabca8))
* 不支持doc文件 ([6b0864a](https://github.com/14790897/handwriting-web/commit/6b0864ad0af49e892647a542af95a243aac5de17))
* 依赖冲突 ([325a5b9](https://github.com/14790897/handwriting-web/commit/325a5b92eb6670a1839ffc3fcd4ec88cf1c6d43d))
* 依赖问题 ([84d6d8d](https://github.com/14790897/handwriting-web/commit/84d6d8d9f325ee4f4764fe2014ddbe4c2db3d41e))
* 修复alpha通道问题 ([c8c6f43](https://github.com/14790897/handwriting-web/commit/c8c6f43738e61b47c37662fa5a261b74f997e4dc))
* 完成任务后删除文件还有减少内存消耗 ([2bc6253](https://github.com/14790897/handwriting-web/commit/2bc6253ffe6b09d637a7103199b3bfa3391a3ee8))
* 延长超时时间 ([e6acd5a](https://github.com/14790897/handwriting-web/commit/e6acd5a1373bf5236e7e108f5519ce491b21f654))
* 文件删除冲突 ([8f7cc6a](https://github.com/14790897/handwriting-web/commit/8f7cc6a47d0c75b36fc71983771096315497bea3))
* 生成环境用hash ([52c650f](https://github.com/14790897/handwriting-web/commit/52c650fce13662e37b154d7d43830aa455453393))
* 错误显示 ([bf0080f](https://github.com/14790897/handwriting-web/commit/bf0080fdec4783e7aec8a114725b84e4503dacc5))


### Features

* CPU usage限制，字数限制 ([af9e8e1](https://github.com/14790897/handwriting-web/commit/af9e8e1d3875bbd0c39d5874000e230cf321a876))
* og  tc ([e3a22b9](https://github.com/14790897/handwriting-web/commit/e3a22b9aa6517c972ca2c50c884123f8ac15c994))
* seo ([f25f7a3](https://github.com/14790897/handwriting-web/commit/f25f7a3452dc99b1e2372bb45f40fbb9bdc9643b))
* 取消预览文本长度限制 ([8dea082](https://github.com/14790897/handwriting-web/commit/8dea0828e9fc6b0a949c5ae942b57ab0829f7fc7))
* 增加介绍页面 ([6412fc9](https://github.com/14790897/handwriting-web/commit/6412fc97d19ba5fedf879aac486672d9ef5275e6))
* 增加重置设置按钮 ([4137f91](https://github.com/14790897/handwriting-web/commit/4137f9136c77c3d1215637473f4eb1660ac0efdd))
* 字体大小检查 ([024eefe](https://github.com/14790897/handwriting-web/commit/024eefe241c259665be06926f0a0d08e4b61fa4d))
* 对上传的图像去除第四通道 ([fea1846](https://github.com/14790897/handwriting-web/commit/fea1846ffe4ecc05c9fca0f8dc08158bab74dd58))
* 文件读取报错处理 ([8c25b53](https://github.com/14790897/handwriting-web/commit/8c25b53adec5f90b71ea5811322a08d907d1eb54))
* 读取docx文件时保留原始格式 ([90fe128](https://github.com/14790897/handwriting-web/commit/90fe128f86d5b80c5cf79b57aeca4527eb39218d))
